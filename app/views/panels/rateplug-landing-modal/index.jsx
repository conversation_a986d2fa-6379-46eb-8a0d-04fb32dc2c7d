const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.rateplug-landing-modal',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    layout: ['layout', 'rateplugLandingModal'],
    welcomeLayout: ['layout', 'rateplugWelcomeModal'],
  },

  getInitialState() {
    return {
      step: 1,
    };
  },

  iconPrimaryColor: '#44B559',

  componentWillMount() {
    if (document.body.dataset.theme === 'more') {
      this.iconPrimaryColor = '#105FA8';
    } else if (document.body.dataset.theme === 'afordal') {
      this.iconPrimaryColor = '#1578FF';
    } else if (document.body.dataset.theme === 'fairway') {
      this.iconPrimaryColor = '#00973A';
    }
  },

  componentDidMount() {
    if (window.rateplug.rp_rate && window.rateplug.rp_payment && window.rateplug.rp_downpayment && !this.hasViewed() && window.rateplug.rp_onboarding !== '0') {
      setTimeout(() => {
        this.actions.common.showRateplugLandingModal();
      }, 100);
    }
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.layout && !prevState.layout) {
      this.actions.analytics.sendEvent('rateplug', 'landing', 'show');
    }
  },

  hasViewed: () => !!window.localStorageAlias.getItem('HA_RATEPLUG_LANDING_ACCEPTED'),
  setHasViewed: () => window.localStorageAlias.setItem('HA_RATEPLUG_LANDING_ACCEPTED', '1'),

  goToStep(step) {
    return () => {
      this.actions.analytics.sendEvent('rateplug', 'landing', 'step', `${step}`);
      this.setState({ step });
    };
  },

  closeModal() {
    if (this.state.step !== 1) {
      this.state.step = 1;
      this.actions.common.hideRateplugLandingModal();
      this.actions.common.showRateplugSpecialFinancingModal();
      this.setHasViewed();
      this.actions.analytics.sendEvent('rateplug', 'landing', 'step', 'close');
    }
  },

  noThanks() {
    this.actions.analytics.sendEvent('rateplug', 'landing', 'no-thanks');
    this.switchToRegularSearch();
  },

  switchToRegularSearch() {
    this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-regular-search');
    window.sessionStorageAlias.setItem('SEARCH_BY_PAYMENT_QS', window.location.search);
    window.location.search = `rp_buyer=${window.rateplug.rp_buyer}&rp_downpayment=${window.rateplug.rp_downpayment}&downPayment=${window.rateplug.rp_downpayment}&theme=${document.body.dataset.theme || 'rateplug'}&saleType=1`;
  },

  renderStep1() {
    return (
      <div className="rateplug-landing-step rateplug-landing-step-1">
        <h2>CHOOSE YOUR EXPERIENCE</h2>
        <div className="rateplug-landing-flex-wrapper">
          <img
            alt="Step 1 graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/rateplug-onboarding-step-1${document.body.dataset.theme === 'more' ? '-more' : ''}.svg`}
          />
          <div>
            <h3>A better way to search for your next home!</h3>
            <p className="mb20">Introducing a new home search experience based on Total Monthly Payment and not the listed Purchase Price.</p>
            <a tabIndex={-1} role="button" className="btn btn-primary btn-block" onClick={this.goToStep(2)}>
              Try Search by Payment + Affordability
            </a>
            <a tabIndex={-1} role="button" className="btn btn-link btn-block" onClick={this.noThanks}>
              No thanks, show the asking prices
            </a>
            <p className="mt20 text-muted"><small><em>* Affordability is determined by a few factors including your  current payment amount and using your current equity as the down payment towards your next home.</em></small></p>
          </div>
        </div>
      </div>
    );
  },

  renderStep2() {
    const downPayment = `$${this.utils.addThousandSep(window.rateplug.rp_downpayment) || '-'}`;
    const payment = `$${this.utils.addThousandSep(window.rateplug.rp_payment) || '-'}`;

    return (
      <div className="rateplug-landing-step rateplug-landing-step-2">
        <h2>BEFORE YOU GET STARTED</h2>
        <div className="rateplug-landing-flex-wrapper">
          <svg width="395" height="273" viewBox="0 0 395 273" fill="none" xmlns="http://www.w3.org/2000/svg">
            <ellipse cx="196.709" cy="260.068" rx="128.55" ry="12.9316" fill="#D9D9D9" />
            <path fill={this.iconPrimaryColor} fillRule="evenodd" clipRule="evenodd" d="M236.936 99.3412C246.854 88.8049 252.979 74.3145 252.979 58.3204C252.979 26.1109 228.14 0 197.5 0C166.859 0 142.02 26.1109 142.02 58.3204C142.02 75.0428 148.715 90.1214 159.442 100.756L197.546 140.811L236.965 99.3722L236.936 99.3412Z" />
            <path d="M239.448 58.1921C239.448 82.3952 220.667 102.016 197.5 102.016C174.333 102.016 155.552 82.3952 155.552 58.1921C155.552 33.9889 174.333 14.3684 197.5 14.3684C220.667 14.3684 239.448 33.9889 239.448 58.1921Z" fill="white" />
            <path d="M211.031 79.0264H184.073V72.7772L193.754 61.9936C196.62 58.7575 198.493 56.5194 199.372 55.2793C200.251 54.0197 200.884 52.8571 201.271 51.7913C201.658 50.7256 201.852 49.621 201.852 48.4778C201.852 46.7725 201.421 45.5033 200.559 44.6701C199.715 43.8369 198.581 43.4202 197.156 43.4202C195.662 43.4202 194.211 43.7981 192.804 44.5538C191.397 45.3095 189.929 46.385 188.399 47.7802L183.968 41.996C185.867 40.2133 187.441 38.9537 188.689 38.2174C189.938 37.481 191.301 36.9191 192.778 36.5315C194.255 36.1246 195.908 35.9211 197.737 35.9211C200.146 35.9211 202.274 36.4056 204.12 37.3745C205.967 38.3433 207.4 39.6998 208.42 41.4437C209.44 43.1877 209.95 45.1836 209.95 47.4314C209.95 49.3885 209.633 51.2294 209 52.954C208.384 54.6592 207.417 56.4129 206.098 58.215C204.797 60.0171 202.494 62.5846 199.188 65.9175L194.229 71.0622V71.4692H211.031V79.0264Z" fill="black" />
            <path d="M25.6484 177.481C25.6484 161.196 37.9589 147.995 53.1447 147.995H341.855C357.041 147.995 369.351 161.196 369.351 177.481V203.282C369.351 219.567 357.041 232.768 341.855 232.768H53.1447C37.9589 232.768 25.6484 219.567 25.6484 203.282V177.481Z" fill="white" />
            <path fillRule="evenodd" clipRule="evenodd" d="M53.1446 143.684H341.855C359.32 143.684 373.411 158.856 373.411 177.481V203.282C373.411 221.907 359.32 237.079 341.855 237.079H53.1446C35.6791 237.079 21.5889 221.907 21.5889 203.282V177.481C21.5889 158.856 35.6791 143.684 53.1446 143.684ZM53.1446 147.995C37.9588 147.995 25.6484 161.196 25.6484 177.481V203.282C25.6484 219.567 37.9588 232.768 53.1446 232.768H341.855C357.041 232.768 369.351 219.567 369.351 203.282V177.481C369.351 161.196 357.041 147.995 341.855 147.995H53.1446Z" fill="#E2E2E2" />
            <text xmlSpace="preserve" textAnchor="center" fontFamily="'Open Sans'" fontWeight="700" fontSize="55" strokeWidth="0" id="svg_9" y="212.70001" x="37.89999" fill="#000000">{`*${payment}/mo`}</text>
            <path fillRule="evenodd" clipRule="evenodd" d="M236.936 99.3412C246.854 88.8049 252.979 74.3145 252.979 58.3204C252.979 26.1109 228.14 0 197.5 0L197.545 140.811L236.965 99.3722L236.936 99.3412Z" fill="black" fillOpacity="0.07" />
          </svg>
          <div>
            <h4>Monthly Payment Pins</h4>
            <p>
              Monthly payment estimates for each property are calculated using your current Equity of
              <strong>{` ${downPayment} `}</strong>
              as a down payment.
            </p>
            <p className="mt10 mb20">
              Monthly price ranges are based on your current payment of
              <strong>{` ${payment} `}</strong>
              .
            </p>
            <p className="mt20 text-muted"><small><em>* Down payment, taxes, Insurance, interest rate, loan term, and APR will alter this amount.</em></small></p>
          </div>
        </div>
        <div tabIndex={-1} role="button" className="rateplug-landing-pager" onClick={this.goToStep(3)}>
          <svg width="51" height="19" viewBox="0 0 51 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.45033 18C14.1236 18 17.9007 14.1881 17.9007 9.5C17.9007 4.81185 14.1236 1 9.45033 1C4.77709 1 1 4.81185 1 9.5C1 14.1881 4.77709 18 9.45033 18Z" fill={this.iconPrimaryColor} stroke={this.iconPrimaryColor} strokeWidth="2" />
            <ellipse cx="43.351" cy="9.5" rx="7.45033" ry="7.5" fill="#979797" />
          </svg>
        </div>
        <a tabIndex={-1} role="button" className="rateplug-landing-last" onClick={this.goToStep(1)}>Last</a>
        <a tabIndex={-1} role="button" className="rateplug-landing-next" onClick={this.goToStep(3)}>Next</a>
      </div>
    );
  },

  renderStep3() {
    return (
      <div className="rateplug-landing-step rateplug-landing-step-3">
        <h2>BEFORE YOU GET STARTED</h2>
        <div className="rateplug-landing-flex-wrapper">
          <img
            className="hidden-mobile"
            alt="Step 3 graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/rateplug-onboarding-step-3-desktop${document.body.dataset.theme === 'more' ? '-more' : ''}.svg`}
          />
          <img
            className="visible-mobile"
            alt="Step 3 graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/rateplug-onboarding-step-3-mobile${document.body.dataset.theme === 'more' ? '-more' : ''}.svg`}
          />
          <div>
            <h4>Modify Your Search</h4>
            <p>
              Adjust your search criteria by using the &quot;Filter&quot; icon at the top right. Narrow the listings shown based on down payment and estimate payments.
            </p>
            <p className="mt10 mb20">
              You can change the down payment and explore other loan programs available in the mortgage calculator.
            </p>
          </div>
        </div>
        <div tabIndex={-1} role="button" className="rateplug-landing-pager" onClick={this.goToStep(2)}>
          <svg width="51" height="19" viewBox="0 0 51 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M41.351 18C46.0242 18 49.8013 14.1881 49.8013 9.5C49.8013 4.81185 46.0242 1 41.351 1C36.6778 1 32.9007 4.81185 32.9007 9.5C32.9007 14.1881 36.6778 18 41.351 18Z" fill={this.iconPrimaryColor} stroke={this.iconPrimaryColor} strokeWidth="2" />
            <ellipse cx="7.45033" cy="9.5" rx="7.45033" ry="7.5" fill="#979797" />
          </svg>
        </div>
        <a tabIndex={-1} role="button" className="rateplug-landing-last" onClick={this.goToStep(2)}>Last</a>
        <a tabIndex={-1} role="button" className="rateplug-landing-next" onClick={this.closeModal}>Next</a>
      </div>
    );
  },

  render() {
    if (!this.state.layout || this.state.welcomeLayout) {
      return null;
    }

    return (
      <Modal show={this.state.layout} onHide={this.closeModal} className={this.utils.isMobile() ? 'rateplug-landing-modal mobile' : 'rateplug-landing-modal'}>
        <Modal.Body>
          {
            [
              this.renderStep1,
              this.renderStep2,
              this.renderStep3,
            ][this.state.step - 1]()
          }
        </Modal.Body>
      </Modal>
    );
  },

});
